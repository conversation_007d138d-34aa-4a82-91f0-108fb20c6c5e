import { loadEnvConfig } from '@next/env';
import { defineConfig } from 'drizzle-kit';
import invariant from 'tiny-invariant';

loadEnvConfig(process.cwd());

const databaseURL = process.env.POSTGRES_URL;
invariant(databaseURL, 'Database URL not Provided!');

export default defineConfig({
  dialect: 'postgresql',
  schema: './src/database/schema.ts',
  out: './src/database/migrations',
  dbCredentials: {
    url: databaseURL,
  },
});
