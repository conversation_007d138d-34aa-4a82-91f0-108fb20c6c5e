'use server';

import { z } from 'zod';

import { db } from '@/database';
import { messagesTable } from '@/database/schema';
import { sendMessageEmail } from '@/lib/email/send-message-email';
import { env } from '@/utils/env';
import { logger } from '@/utils/logger';

import { authActionClient } from './safe-action';

export const sendMessageAction = authActionClient
  .schema(
    z.object({ message: z.string().trim().min(1, 'Message is required') })
  )
  .metadata({ name: 'send-message' })
  .action(async ({ ctx, parsedInput }) => {
    try {
      await db.insert(messagesTable).values({
        message: parsedInput.message,
        userId: ctx.user.id,
      });

      await sendMessageEmail({
        recipient: env.EMAIL_FROM,
        receiverName: 'Tehseen',
        senderEmail: ctx.user.email,
        senderName: ctx.user.name,
        message: parsedInput.message,
      });

      return { success: true };
    } catch (error) {
      logger.error({ error }, 'Failed to send message:');
      throw error;
    }
  });
