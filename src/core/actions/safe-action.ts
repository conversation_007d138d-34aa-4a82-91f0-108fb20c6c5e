import {
  DEFAULT_SERVER_ERROR_MESSAGE,
  createSafeActionClient,
} from 'next-safe-action';
import { headers } from 'next/headers';
import { z } from 'zod';

import { auth } from '@/lib/auth';
import { inMemoryRateLimiter } from '@/lib/rate-limiter/in-memory';
import { env } from '@/utils/env';
import { getClientIp } from '@/utils/ip';
import { logger } from '@/utils/logger';

const secure = new URL(env.NEXT_PUBLIC_SITE_URL).protocol === 'https:';

export const actionClient = createSafeActionClient({
  handleServerError(error) {
    logger.error({ error }, '❌ SERVER ERROR');

    if (error instanceof Error) {
      return error.message;
    }

    return DEFAULT_SERVER_ERROR_MESSAGE;
  },
  defineMetadataSchema() {
    return z.object({
      name: z.string(),
    });
  },
});

export const rateLimitedActionClient = actionClient.use(
  async ({ next, metadata }) => {
    const response = next({ ctx: {} });
    if (secure) {
      // Built-in rate limiter to help manage traffic and prevent abuse.
      // Does not support serverless rate limiting, because the storage is in-memory.
      const ip = await getClientIp();
      const uniqueIdentifier = `${ip}-${metadata?.name}`;
      const limiter = inMemoryRateLimiter({
        intervalInMs: 60 * 1000, // 1 minute
      });
      const { isRateLimited, remaining } = limiter.check(10, uniqueIdentifier); // 10 requests per minute
      if (isRateLimited) {
        logger.error({ remaining }, 'Rate limit exceeded');
        throw new Error('Rate limit exceeded');
      }
      return response;
    }
    return response;
  }
);

export const authActionClient = actionClient.use(async ({ next }) => {
  const ctx = await auth.api.getSession({ headers: await headers() });
  if (!ctx) {
    throw new Error('Unauthorized');
  }

  const { user } = ctx;

  return next({ ctx: { user } });
});
