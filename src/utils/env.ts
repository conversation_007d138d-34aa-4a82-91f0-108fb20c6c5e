import { createEnv } from '@t3-oss/env-nextjs';
import { z } from 'zod';

export const env = createEnv({
  server: {
    NODE_ENV: z.enum(['development', 'test', 'production']),
    BETTER_AUTH_SECRET: z.string(),
    BETTER_AUTH_GOOGLE_ID: z.string(),
    BETTER_AUTH_GOOGLE_SECRET: z.string(),
    POSTGRES_URL: z.string().url(),
    EMAIL_FROM: z.string().email(),
    EMAIL_NODEMAILER_URL: z.string().url(),
  },
  client: {
    NEXT_PUBLIC_SITE_URL: z.string().url(),
  },
  runtimeEnv: {
    // Server Keys
    NODE_ENV: process.env.NODE_ENV,
    BETTER_AUTH_SECRET: process.env.BETTER_AUTH_SECRET,
    BETTER_AUTH_GOOGLE_ID: process.env.BETTER_AUTH_GOOGLE_ID,
    BETTER_AUTH_GOOGLE_SECRET: process.env.BETTER_AUTH_GOOGLE_SECRET,
    EMAIL_FROM: process.env.EMAIL_FROM,
    EMAIL_NODEMAILER_URL: process.env.EMAIL_NODEMAILER_URL,
    // Client Keys
    POSTGRES_URL: process.env.POSTGRES_URL,
    NEXT_PUBLIC_SITE_URL: process.env.NEXT_PUBLIC_SITE_URL,
  },
});
