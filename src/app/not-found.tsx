import { ArrowLeftIcon } from 'lucide-react';
import Link from 'next/link';

import { buttonVariants } from '@/components/ui/button';
import { SiteConfig } from '@/configuration';

export default function NotFound() {
  return (
    <div className="h-[50dvh] flex flex-col items-center justify-center gap-8 text-center">
      <div className="space-y-4">
        <h1 className="text-4xl font-bold">404</h1>
        <h2 className="text-xl font-semibold">Oops! Page not found</h2>
        <p className="text-secondary-foreground">
          The page you&apos;re looking for doesn&apos;t exist or has been moved.
        </p>
      </div>

      <div>
        <Link
          href={SiteConfig.paths.home.link}
          className={buttonVariants({ variant: 'secondary' })}
        >
          <ArrowLeftIcon className="mr-2 size-4" />
          Back to Home
        </Link>
      </div>
    </div>
  );
}
