import type { Metadata } from 'next';

import {
  CardList,
  CardListItem,
  CardListItemDescription,
  CardListItemTitle,
} from '@/components/card-list';

import { projects } from '@/velite';

export const metadata: Metadata = {
  title: 'Projects',
  description: 'A summary of my work and contributions.',
};

export default function ProjectPage() {
  return (
    <div>
      <h1 className="font-medium mb-5">Projects</h1>

      <CardList>
        {projects.map(({ id, title, description, projectLink, githubLink }) => (
          <CardListItem key={id} href={projectLink} githubLink={githubLink}>
            <CardListItemTitle>{title}</CardListItemTitle>
            <CardListItemDescription>{description}</CardListItemDescription>
          </CardListItem>
        ))}
      </CardList>
    </div>
  );
}
