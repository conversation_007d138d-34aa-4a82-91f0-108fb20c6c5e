import type { Metadata } from 'next';

import { Analytics } from '@vercel/analytics/next';
import { GeistSans } from 'geist/font/sans';

import { Footer } from '@/components/footer';
import { Header } from '@/components/header';
import { Providers } from '@/components/providers';

import { cn } from '@/utils';
import { createMetadata } from '@/utils/seo';

import './globals.css';

export const metadata: Metadata = createMetadata({});

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className={cn(GeistSans.variable)} suppressHydrationWarning>
      <body className="relative" suppressHydrationWarning>
        <Providers>
          <main className="mx-auto max-w-2xl px-6 antialiased mt-6 sm:mt-16 mb-24 space-y-10">
            <Header />
            <div>{children}</div>
            <Footer />
          </main>

          {/* <div className="w-full fixed bottom-4">
            <FloatingNavbar />
          </div> */}
        </Providers>
        <Analytics />
      </body>
    </html>
  );
}
