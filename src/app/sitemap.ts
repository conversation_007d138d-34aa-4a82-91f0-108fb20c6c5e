import type { MetadataRoute } from 'next';

import { SiteConfig } from '@/configuration';

const siteUrl = SiteConfig.site.url;

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  return [
    {
      url: siteUrl,
      lastModified: new Date(),
      changeFrequency: 'daily' as const,
      priority: 1,
    },
    ...getRoutesSitemap(),
  ];
}

function getRoutesSitemap() {
  return Object.values(SiteConfig.paths).map((route) => ({
    url: `${siteUrl}${route.link}`,
    lastModified: new Date(),
    changeFrequency: 'monthly' as const,
    priority: 0.8,
  }));
}
