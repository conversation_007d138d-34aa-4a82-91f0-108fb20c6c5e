import { ImageResponse } from 'next/og';
import type { NextRequest } from 'next/server';

import { SiteConfig } from '@/configuration';

export const runtime = 'edge';

export async function GET(req: NextRequest) {
  const { searchParams } = req.nextUrl;
  const ogTitle = decodeURIComponent(searchParams.get('title') ?? '');

  const backgroundImage = `url(${SiteConfig.site.url}/og/background.png)`;
  const kaiseiFont = await fetch(
    new URL('./geist-semibold.ttf', import.meta.url)
  ).then((res) => res.arrayBuffer());

  return new ImageResponse(
    <div
      style={{
        height: '100%',
        width: '100%',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'flex-start',
        justifyContent: 'flex-end',
        backgroundImage,
        backgroundRepeat: 'no-repeat',
        backgroundPosition: 'center',
        backgroundSize: 'cover',
      }}
    >
      <div
        style={{
          marginLeft: 120,
          marginRight: 120,
          marginBottom: 120,
          display: 'flex',
          fontSize: 130,
          fontFamily: 'Giest Sans',
          letterSpacing: '-0.05em',
          fontStyle: 'normal',
          fontWeight: 600,
          color: 'white',
          lineHeight: '140px',
          whiteSpace: 'pre-wrap',
        }}
      >
        {ogTitle}
      </div>
    </div>,
    {
      width: 1920,
      height: 1080,
      fonts: [
        {
          name: 'Giest Sans',
          data: kaiseiFont,
          style: 'normal',
          weight: 600,
        },
      ],
    }
  );
}
