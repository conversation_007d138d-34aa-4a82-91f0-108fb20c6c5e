import type { Metadata } from 'next';

import { notFound } from 'next/navigation';

import { MDXContent } from '@/components/mdx-content';

import { SiteConfig } from '@/configuration';
import { posts } from '@/velite';

const getPostBySlug = (slug: string) => {
  return posts.find((p) => p.slug === slug);
};

export default async function BlogChildPage({ params }: NextPageProps) {
  const { slug } = await params;
  const post = getPostBySlug(slug);
  if (post == null) return notFound();

  return (
    <div>
      <MDXContent code={post.body} />
    </div>
  );
}

export async function generateMetadata({
  params,
}: NextPageProps): Promise<Metadata> {
  const { slug } = await params;
  const post = getPostBySlug(slug);
  if (!post) {
    return {};
  }
  const { title, description, date } = post;
  const ogImage = `${SiteConfig.site.url}/og?title=${title}`;
  const url = `${SiteConfig.site.url}/blog/${post.slug}`;

  return {
    title,
    description,
    openGraph: {
      title,
      description,
      type: 'article',
      publishedTime: date,
      url,
      images: [
        {
          url: ogImage,
        },
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title,
      description,
      images: [ogImage],
    },
  };
}
