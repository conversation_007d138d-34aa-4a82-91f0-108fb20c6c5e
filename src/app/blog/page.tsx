import type { Metadata } from 'next';

import { CardList, CardListItem, HoverLine } from '@/components/card-list';

import { formatBlogDate } from '@/utils/date';
import { posts } from '@/velite';

export const metadata: Metadata = {
  title: 'Blog',
  description: 'The list of articles that I wrote',
};

export default function BlogPage() {
  return (
    <div>
      <h1 className="font-medium mb-5">Blogs</h1>

      <CardList>
        {posts.map(({ id, permalink, title, date }) => (
          <CardListItem
            key={id}
            href={permalink}
            className="sm:flex-row sm:gap-4 sm:p-0 sm:mx-0 sm:hover:bg-transparent sm:items-center sm:justify-between text-secondary-foreground sm:hover:text-accent-foreground"
          >
            <h3 className="transition-smooth max-w-sm overflow-ellipsis text-accent-foreground sm:text-secondary-foreground sm:group-hover:text-accent-foreground">
              {title}
            </h3>
            <HoverLine />
            <span>{formatBlogDate(date)}</span>
          </CardListItem>
        ))}
      </CardList>
    </div>
  );
}
