import type { authClient } from '@/auth/client';

type ErrorTypes = Partial<
  Record<
    keyof typeof authClient.$ERROR_CODES,
    {
      en: string;
      es: string;
    }
  >
>;
const errorCodes = {
} satisfies ErrorTypes;

export const getAuthErrorMessage = (code: string, lang: 'en' | 'es') => {
  if (code in errorCodes) {
    return errorCodes[code as keyof typeof errorCodes][lang];
  }
  return '';
};
