import { FolderIcon, HomeIcon, PencilLineIcon } from 'lucide-react';

import { GithubIcon } from '@/components/icons/github';
import { LinkedInIcon } from '@/components/icons/linkedin';
import { TwitterIcon } from '@/components/icons/twitter';

import { env } from '@/utils/env';

export const SiteConfig = {
  site: {
    name: 'Tehseen',
    url: env.NEXT_PUBLIC_SITE_URL,
    twitterHandle: '@tehseen_js',
  },
  paths: {
    home: { title: 'Home', link: '/', Icon: HomeIcon },
    projects: { title: 'Projects', link: '/projects', Icon: FolderIcon },
    blog: { title: 'Blog', link: '/blog', Icon: PencilLineIcon },
  },
  socials: {
    github: {
      title: 'Github',
      link: 'https://github.com/MdTehseenKhan',
      Icon: GithubIcon,
    },
    linkedin: {
      title: 'Linkedin',
      link: 'https://linkedin.com/in/md-tehseen-khan',
      Icon: LinkedInIcon,
    },
    twitter: {
      title: 'Twitter',
      link: 'https://twitter.com/tehseen_js',
      Icon: TwitterIcon,
    },
  },
};
