import React from 'react';

import { Hr, Img, Link, Row, Section, Text } from '@react-email/components';

import { SiteConfig } from '@/configuration';

interface EmailFooterProps {
  receiverEmail: string;
}
export const EmailFooter = ({ receiverEmail }: EmailFooterProps) => {
  const siteURL = SiteConfig.site.url;
  const siteLogoIcon = '/avatar.png';

  return (
    <Section className="mt-10 text-xs text-gray-500">
      <Row>
        <Hr />
      </Row>
      <Row>
        <Text className="text-xs">
          This email was sent to{' '}
          <Link
            href={`mailto:${receiverEmail}`}
            className="text-black underline underline-offset-2 font-semibold"
          >
            {receiverEmail}
          </Link>
          . If you do not recognize this message, please ignore it.
        </Text>
      </Row>
      <Row className="mt-6 text-gray-950">
        <Link href={siteURL}>
          <Img
            src={siteLogoIcon}
            alt={SiteConfig.site.name}
            width={24}
            height={24}
          />
        </Link>
      </Row>
    </Section>
  );
};
