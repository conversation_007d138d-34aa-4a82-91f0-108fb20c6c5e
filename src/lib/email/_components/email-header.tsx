import React from 'react';

import { SiteConfig } from '@/configuration';
import { Img, Link, Section } from '@react-email/components';

export const EmailHeader = () => {
  const siteURL = SiteConfig.site.url;
  const productName = SiteConfig.site.name;
  const siteLogoIcon = '/avatar.png';

  return (
    <Section className="mb-6">
      <Link href={siteURL}>
        <Img src={siteLogoIcon} alt={productName} width={40} height={40} />
      </Link>
    </Section>
  );
};
