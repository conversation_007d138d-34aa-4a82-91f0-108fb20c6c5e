import nodemailer from 'nodemailer';
import type SMTPTransport from 'nodemailer/lib/smtp-transport';

import { env } from '@/utils/env';
import { type NodemailerTransport, detectTransport } from './detect-transport';

type EmailPayload = {
  recipient: string;
  subject: string;
  text: string;
  html: string;
  replyTo?: string;
};

type EmailProvider = {
  sendEmail(payload: EmailPayload): Promise<unknown>;
};

class NodemailerEmailProvider implements EmailProvider {
  private readonly from: string;
  private readonly transport: NodemailerTransport;

  constructor() {
    const from = env.EMAIL_FROM;
    if (!from) {
      throw new Error('Missing EMAIL_FROM in environment configuration');
    }

    this.from = from;
    this.transport = detectTransport();
  }

  public async sendEmail(
    payload: EmailPayload
  ): Promise<SMTPTransport.SentMessageInfo> {
    return await nodemailer.createTransport(this.transport).sendMail({
      from: this.from,
      to: payload.recipient,
      subject: payload.subject,
      html: payload.html,
      text: payload.text,
      replyTo: payload.replyTo,
    });
  }
}

export const EmailProvider = new NodemailerEmailProvider();
