import type { NextTopLoaderProps } from 'nextjs-toploader';
import type { PropsWithChildren } from 'react';

import { ThemeProvider } from 'next-themes';
import NextTopLoader from 'nextjs-toploader';
import { Toaster } from 'sonner';

import { TooltipProvider } from '@/components/ui/tooltip';

const topLoaderConfig: NextTopLoaderProps = {
  height: 2,
  speed: 200,
  crawl: true,
  color: '#fff',
  easing: 'ease',
  crawlSpeed: 200,
  showSpinner: false,
  initialPosition: 0.08,
  shadow: '0 0 10px #fff,0 0 5px #fff',
};

export const Providers = ({ children }: PropsWithChildren) => {
  return (
    <ThemeProvider
      attribute="class"
      defaultTheme="dark"
      disableTransitionOnChange
    >
      <TooltipProvider>
        <NextTopLoader {...topLoaderConfig} />

        {children}

        <Toaster position="top-right" />
      </TooltipProvider>
    </ThemeProvider>
  );
};
