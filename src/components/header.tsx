import Link from 'next/link';

import { ToggleTheme } from '@/components/toggle-theme';

import { SiteConfig } from '@/configuration';

// import { Button } from '@/components/ui/button'

// const navItems = {
//   home: {
//     path: '/',
//   },
//   work: {
//     path: '/projects',
//   },
//   blog: {
//     path: '/blog',
//   },
// }

export const Header = () => {
  return (
    <header className="flex justify-between">
      <div className="flex flex-col font-medium">
        <Link href={SiteConfig.paths.home.link}><PERSON></Link>
        <h1 className="leading-none text-secondary-foreground">
          Software Engineer
        </h1>
      </div>

      <div className="flex">
        {/* {Object.entries(navItems).map(([name, { path }]) => {
          return (
            <Button key={path} variant="ghost" asChild>
              <Link href={path}>{name}</Link>
            </Button>
          )
        })} */}

        <ToggleTheme />
      </div>
    </header>
  );
};
