import { ArrowUpRightIcon } from 'lucide-react';
import Link from 'next/link';

import { GithubIcon } from '@/components/icons/github';

import { cn } from '@/utils';

interface CardListProps extends React.PropsWithChildren {
  cols?: 'one' | 'two';
  className?: string;
}

const CardList: React.FC<CardListProps> = ({ cols, className, children }) => {
  return (
    <div
      className={cn(
        'grid grid-cols-1 gap-2',
        { 'sm:grid-cols-2 sm:gap-8': cols === 'two' },
        className
      )}
    >
      {children}
    </div>
  );
};
CardList.displayName = 'CardList';

interface CardListItemProps extends React.PropsWithChildren {
  href: string;
  className?: string;
  isExternal?: boolean;
  githubLink?: string;
}

const CardListItem: React.FC<CardListItemProps> = ({
  href,
  // githubLink,
  className,
  children,
}) => {
  const isExternal = href.startsWith('http');
  const iconStyles =
    'h-5 stroke-secondary-foreground hover:stroke-accent-foreground opacity-0 group-hover:opacity-100 transition-smooth';

  return (
    <Link
      href={href}
      target={isExternal ? '_blank' : '_self'}
      className={cn(
        'transition-smooth relative group -mx-4 flex flex-col rounded-md px-4 py-3 hover:bg-accent',
        className
      )}
    >
      {/* TODO: RESOLVE HYDRATION ERROR */}
      {/* {githubLink && (
          <Link
            href={githubLink}
            target="_blank"
            className="absolute top-3 right-10"
          >
            <GithubIcon className={iconStyles} />
          </Link>
        )} */}

      <span className="absolute top-3 right-4">
        {isExternal && <ArrowUpRightIcon className={iconStyles} />}
      </span>

      {children}
    </Link>
  );
};
CardListItem.displayName = 'CardListItem';

const CardListItemTitle = ({ children }: React.PropsWithChildren) => {
  return <span>{children}</span>;
};
CardListItemTitle.displayName = 'CardListItemTitle';

const CardListItemDescription = ({
  className,
  children,
}: {
  className?: string;
  children?: string;
}) => {
  return (
    <span className={cn('text-secondary-foreground line-clamp-3', className)}>
      {children}
    </span>
  );
};
CardListItemDescription.displayName = 'CardListItemDescription';

const HoverLine = () => {
  return (
    <span className="hidden sm:flex relative h-px flex-1 bg-secondary-foreground/50">
      <span className="transition-all flex w-0 h-full group-hover:w-full bg-accent-foreground" />
    </span>
  );
};

export {
  CardList,
  CardListItem,
  CardListItemTitle,
  CardListItemDescription,
  HoverLine,
};
