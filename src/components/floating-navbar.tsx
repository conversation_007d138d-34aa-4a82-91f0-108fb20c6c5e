'use client';

import { ArrowUpRight } from 'lucide-react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';

import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/ui/tooltip';

import { SiteConfig } from '@/configuration';
import { cn } from '@/utils';
import { buttonVariants } from './ui/button';

export const FloatingNavbar = () => {
  const pathname = usePathname();

  return (
    <div className="flex justify-center">
      <div className="z-50 flex gap-2 p-2 rounded-full border border-secondary backdrop-blur-md bg-secondary/80 text-secondary-foreground shadow-md">
        {Object.values(SiteConfig.paths).map(({ title, link, Icon }) => (
          <Tooltip key={title}>
            <TooltipTrigger
              className={cn(
                buttonVariants({ variant: 'ghost', size: 'icon' }),
                'rounded-full hover:text-secondary-foreground',
                {
                  'bg-accent-foreground text-secondary pointer-events-none':
                    pathname.split('/')[1] === link.split('/')[1],
                }
              )}
            >
              <Link href={link} target={isExternal(link) ? '_blank' : '_self'}>
                <Icon className="" />
              </Link>
            </TooltipTrigger>
            <TooltipContent>
              <span>{title}</span>
              {isExternal(link) && <ArrowUpRight className="inline-flex h-4" />}
            </TooltipContent>
          </Tooltip>
        ))}
      </div>
    </div>
  );
};

function isExternal(link: string) {
  return link.startsWith('http');
}
