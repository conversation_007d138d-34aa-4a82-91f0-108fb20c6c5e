import type { ComponentPropsWithoutRef } from 'react';

import Image from 'next/image';
import Link from 'next/link';

import { Codeblock } from '@/components/ui/codeblock';
import { useMDXComponent } from '@/core/hooks/use-mdx-component';
import { cn } from '@/utils';

const MDXImage = (props: ComponentPropsWithoutRef<'img'>) => {
  return (
    <div className="my-6 w-full flex justify-center">
      <img
        {...props}
        alt={props.alt || 'Content image'}
        className={cn(
          'rounded-md max-h-[500px] w-auto transition-smooth',
          props.className
        )}
        loading="lazy"
      />
    </div>
  );
};

const components = {
  Image,
  Codeblock,
  h1: (props: ComponentPropsWithoutRef<'h1'>) => (
    <h1
      className="font-medium pt-12 mb-4 text-foreground tracking-tighter fade-in"
      {...props}
    />
  ),
  h2: (props: ComponentPropsWithoutRef<'h2'>) => (
    <h2
      className="text-foreground font-medium mt-10 mb-4 tracking-tighter"
      {...props}
    />
  ),
  h3: (props: ComponentPropsWithoutRef<'h3'>) => (
    <h3
      className="text-foreground font-medium mt-8 mb-3 tracking-tighter"
      {...props}
    />
  ),
  h4: (props: ComponentPropsWithoutRef<'h4'>) => (
    <h4
      className="text-foreground font-medium mt-6 mb-2 tracking-tighter"
      {...props}
    />
  ),
  p: (props: ComponentPropsWithoutRef<'p'>) => (
    <p className="text-secondary-foreground leading-relaxed mb-4" {...props} />
  ),
  ol: (props: ComponentPropsWithoutRef<'ol'>) => (
    <ol
      className="text-secondary-foreground list-decimal pl-5 space-y-2 mb-6"
      {...props}
    />
  ),
  ul: (props: ComponentPropsWithoutRef<'ul'>) => (
    <ul
      className="text-secondary-foreground list-disc pl-5 space-y-2 mb-6"
      {...props}
    />
  ),
  li: (props: ComponentPropsWithoutRef<'li'>) => (
    <li className="pl-1 mb-1" {...props} />
  ),
  em: (props: ComponentPropsWithoutRef<'em'>) => (
    <em className="font-medium italic" {...props} />
  ),
  strong: (props: ComponentPropsWithoutRef<'strong'>) => (
    <strong className="text-foreground font-medium" {...props} />
  ),
  blockquote: (props: ComponentPropsWithoutRef<'blockquote'>) => (
    <blockquote
      className="ml-[0.075em] border-l-2 border-border pl-4 py-1 text-secondary-foreground my-6 italic"
      {...props}
    />
  ),
  hr: (props: ComponentPropsWithoutRef<'hr'>) => (
    <hr className="my-8 border-border" {...props} />
  ),
  img: MDXImage,
  table: (props: ComponentPropsWithoutRef<'table'>) => (
    <div className="my-6 w-full overflow-x-auto rounded-md border border-border">
      <table className="w-full" {...props} />
    </div>
  ),
  tr: (props: ComponentPropsWithoutRef<'tr'>) => (
    <tr className="m-0 border-t border-border p-0 even:bg-muted" {...props} />
  ),
  th: (props: ComponentPropsWithoutRef<'th'>) => (
    <th
      className="border-r border-border px-4 py-2 text-left font-medium [&[align=center]]:text-center [&[align=right]]:text-right"
      {...props}
    />
  ),
  td: (props: ComponentPropsWithoutRef<'td'>) => (
    <td
      className="border-r border-border px-4 py-2 text-left [&[align=center]]:text-center [&[align=right]]:text-right"
      {...props}
    />
  ),
  pre: (props: ComponentPropsWithoutRef<'pre'>) => (
    <pre
      className="mb-6 mt-4 overflow-x-auto rounded-md border border-border bg-muted py-4"
      {...props}
    />
  ),
  code: (props: ComponentPropsWithoutRef<'code'>) => (
    <code
      className="rounded-md bg-muted px-1.5 py-0.5 font-medium text-foreground text-sm"
      {...props}
    />
  ),
  a: ({ href, ...props }: ComponentPropsWithoutRef<'a'>) => {
    const className =
      'text-secondary-foreground hover:text-accent-foreground underline underline-offset-4 decoration-[0.1em] decoration-secondary transition-smooth';
    if (href?.startsWith('/')) {
      return <Link href={href} className={className} {...props} />;
    }
    if (href?.startsWith('#')) {
      return <a href={href} className={className} {...props} />;
    }
    return (
      <a
        href={href}
        target="_blank"
        rel="noopener noreferrer"
        className={className}
        {...props}
      />
    );
  },
};

interface MDXContentProps {
  code: string;
  className?: string;
}

export function MDXContent({ code, className }: MDXContentProps) {
  const Component = useMDXComponent(code);
  return (
    <div className={cn('prose prose-neutral dark:prose-invert', className)}>
      <Component components={components} />
    </div>
  );
}
