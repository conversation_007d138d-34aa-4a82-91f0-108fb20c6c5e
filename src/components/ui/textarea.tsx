import type * as React from 'react';

import { cn } from '@/utils/index';

function Textarea({ className, ...props }: React.ComponentProps<'textarea'>) {
  return (
    <textarea
      data-slot="textarea"
      className={cn(
        'border-input placeholder:text-muted-foreground aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-primary-foreground flex field-sizing-content min-h-16 w-full rounded-md border bg-muted px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none disabled:cursor-not-allowed disabled:placeholder:opacity-50 disabled:opacity-50 md:text-sm focus-visible:outline-none',
        className
      )}
      {...props}
    />
  );
}

export { Textarea };
