import type { VariantProps } from 'class-variance-authority';

import { cva } from 'class-variance-authority';
import React from 'react';

import { cn } from '@/utils';

const pingDotVariants = cva(
  'relative inline-flex rounded-full before:absolute before:inset-0 before:animate-ping before:rounded-full',
  {
    variants: {
      color: {
        green:
          'bg-green-500 before:bg-green-400 before:border before:border-green-600',
        red: 'bg-red-500 before:bg-red-400 before:border before:border-red-600',
      },
      size: {
        sm: 'size-2',
        md: 'size-3',
        lg: 'size-4',
      },
    },
    defaultVariants: {
      color: 'green',
      size: 'sm',
    },
  }
);

const PingDot = React.forwardRef<
  HTMLSpanElement,
  React.HTMLAttributes<HTMLSpanElement> & VariantProps<typeof pingDotVariants>
>(({ className, color, size, ...props }, ref) => (
  <span
    ref={ref}
    className={cn(pingDotVariants({ color, size }), className)}
    {...props}
  />
));

PingDot.displayName = 'PingDot';

export { PingDot, pingDotVariants };
