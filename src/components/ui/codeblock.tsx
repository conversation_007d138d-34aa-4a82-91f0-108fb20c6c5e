import type { BundledLanguage, BundledTheme } from 'shiki';

import { codeToHtml } from 'shiki';

import { CopyToClipboard } from '@/components/ui/copy-to-clipboard';

type CodeblockProps = {
  code: string;
  lang?: BundledLanguage;
  theme?: BundledTheme;
  filename?: string;
};

const Codeblock: React.FC<CodeblockProps> = async ({
  code,
  lang = 'typescript',
  theme = 'material-theme-darker',
  filename,
}) => {
  const __html = await codeToHtml(code, { lang, theme });

  return (
    <div className="rounded-md mb-4">
      <div className="flex items-center justify-between rounded-t-md bg-gradient-to-r from-neutral-900 to-neutral-800 text-sm h-11 px-4">
        {filename && <span className="text-gray-200">{filename}</span>}
        <CopyToClipboard code={code} />
      </div>
      <div
        className="rounded-b-md border-t-2 border-neutral-700 text-sm [&>pre]:overflow-x-auto [&>pre]:!bg-neutral-900 [&>pre]:py-3 [&>pre]:pl-4 [&>pre]:pr-5 [&>pre]:leading-snug [&_code]:block [&_code]:w-fit [&_code]:min-w-full overflow-y-auto"
        dangerouslySetInnerHTML={{ __html }}
      />
    </div>
  );
};
Codeblock.displayName = 'Codeblock';

export { Codeblock };
