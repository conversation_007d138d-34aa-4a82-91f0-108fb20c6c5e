import * as React from 'react'

import { cn } from '@/utils'

export interface InputProps
  extends React.InputHTMLAttributes<HTMLInputElement> {}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ className, type, disabled, children, ...props }, ref) => {
    return (
      <div
        className={cn(
          'flex items-center rounded-md border border-input bg-muted dark:bg-primary-foreground overflow-hidden',
          disabled && 'bg-muted',
          className
        )}
      >
        <input
          ref={ref}
          type={type}
          disabled={disabled}
          className={cn(
            'flex w-full h-10 py-2 px-3 bg-inherit text-base placeholder:text-muted-foreground disabled:placeholder:opacity-50 disabled:cursor-not-allowed focus-visible:outline-none'
          )}
          {...props}
        />
        {children}
      </div>
    )
  }
)
Input.displayName = 'Input'

export { Input }
