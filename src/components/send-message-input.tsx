'use client';

import { useCallback, useState } from 'react';
import { toast } from 'sonner';

import { GoogleIcon } from '@/components/icons/google';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';

import { sendMessageAction } from '@/core/actions/message';
import { signIn } from '@/lib/auth/client';

interface SendMessageInputProps {
  sessionExists?: boolean;
}

export function SendMessageInput({ sessionExists }: SendMessageInputProps) {
  const [message, setMessage] = useState('');

  const handleSignInWithGoogle = useCallback(() => {
    const promise = signIn.social({ provider: 'google' });
    toast.promise(promise, {
      loading: 'Authenticating...',
      error: 'Authentication error.',
    });
  }, [signIn]);

  const handleSendMessage = useCallback(() => {
    const promise = sendMessageAction({ message });
    toast.promise(promise, {
      loading: 'Sending message...',
      error: 'Failed to send message.',
      success: () => {
        setMessage('');
        return 'Message sent successfully.';
      },
    });
  }, [message]);

  const handleKeyDown = useCallback(
    (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
      if (e.key === 'Enter' && !e.shiftKey) {
        e.preventDefault();
        if (!sessionExists) {
          handleSignInWithGoogle();
        } else {
          handleSendMessage();
        }
      }
    },
    [sessionExists, handleSignInWithGoogle, handleSendMessage]
  );

  const variant = !sessionExists ? 'transparent' : 'default';
  const onClick = !sessionExists ? handleSignInWithGoogle : handleSendMessage;

  return (
    <div className="relative w-full">
      <Textarea
        value={message}
        onChange={(e) => setMessage(e.target.value)}
        placeholder="Send me a message"
        className="resize-none min-h-0 h-10 pr-14"
        onKeyDown={handleKeyDown}
        disabled={!sessionExists}
        rows={1}
      />
      <Button
        size="xs"
        type="button"
        className="gap-1 absolute right-2 bottom-1"
        variant={variant}
        onClick={onClick}
      >
        {getButtonContent(sessionExists)}
      </Button>
    </div>
  );
}

function getButtonContent(sessionExists = false) {
  if (!sessionExists) {
    return (
      <>
        <GoogleIcon />
        <span>Sign in</span>
      </>
    );
  }

  return 'Send';
}
