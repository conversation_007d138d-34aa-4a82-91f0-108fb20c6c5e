'use client';

import Link from 'next/link';
import { useCallback } from 'react';
import { toast } from 'sonner';

import { SendMessageInput } from '@/components/send-message-input';
import { Button, buttonVariants } from '@/components/ui/button';
import { If } from '@/components/ui/if';

import { SiteConfig } from '@/configuration';
import { signOut, useSession } from '@/lib/auth/client';

export function Footer() {
  const { data: session } = useSession();
  const sessionExists = !!session?.user.id;

  const handleSignOut = useCallback(() => {
    const promise = signOut();
    toast.promise(promise, {
      loading: 'Signing out...',
      error: 'Failed to sign out.',
      success: 'Signed out successfully.',
    });
  }, [signOut]);

  return (
    <footer className="mt-auto space-y-5">
      <h1 className="font-medium">Get in Touch</h1>

      <SendMessageInput sessionExists={sessionExists} />

      <div className="flex gap-4 justify-between items-center">
        <div>
          {Object.values(SiteConfig.socials).map(({ title, link, Icon }) => {
            return (
              <Link
                key={title}
                href={link}
                target="_blank"
                className={buttonVariants({
                  variant: 'ghost',
                  size: 'icon',
                })}
              >
                <Icon />
              </Link>
            );
          })}
        </div>

        <If condition={sessionExists}>
          <Button variant="link" size="link" onClick={handleSignOut}>
            Sign Out
          </Button>
        </If>
      </div>
    </footer>
  );
}
