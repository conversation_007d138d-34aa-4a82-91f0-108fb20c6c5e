import rehypeAutolinkHeadings from 'rehype-autolink-headings';
import rehypeSlug from 'rehype-slug';
import { defineCollection, defineConfig, s } from 'velite';

const computedFields = <T extends { permalink: string }>(data: T) => ({
  ...data,
  id: crypto.randomUUID(),
  slug: data.permalink.split('/').slice(1).join('/'),
});

const projects = defineCollection({
  name: 'Project',
  pattern: 'projects/**/*.mdx',
  schema: s
    .object({
      title: s.string().max(99),
      description: s.string().max(999).optional(),
      type: s.enum(['Personal', 'Client']),
      permalink: s.path(),
      projectLink: s.string().url(),
      githubLink: s.string().url(),
      date: s.isodate(),
      published: s.boolean().default(true),
      tags: s.array(s.string()).optional(),
    })
    .transform(computedFields),
});

const posts = defineCollection({
  name: 'Post',
  pattern: 'blog/**/*.mdx',
  schema: s
    .object({
      title: s.string().max(99),
      description: s.string().max(999).optional(),
      date: s.isodate(),
      published: s.boolean().default(true),
      tags: s.array(s.string()).optional(),
      body: s.mdx(),
      permalink: s.path(),
      metadata: s.metadata(),
      /* interface Metadata { 
          readingTime: number, //  in minutes  
          wordCount: number
        } */
    })
    .transform(computedFields),
});

export default defineConfig({
  root: 'content',
  collections: { posts, projects },
  output: {
    data: '.velite',
    assets: 'public/static',
    base: '/static/',
    name: '[name]-[hash:6].[ext]',
    clean: true,
  },
  mdx: {
    rehypePlugins: [
      rehypeSlug,
      [
        rehypeAutolinkHeadings,
        {
          behavior: 'wrap',
          properties: {
            className: ['subheading-anchor'],
            ariaLabel: 'Link to section',
          },
        },
      ],
    ],
    remarkPlugins: [],
  },
});
